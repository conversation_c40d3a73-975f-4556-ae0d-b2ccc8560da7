import asyncio
import aiohttp
import aiofiles
import os
import tiktoken
import time
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import filedialog
import random

# 双API Key配置
API_KEYS = [
    'sk-oyCIv8iRpgC265xq24396dB6AbEc4e908342F8876fFe389d',
    'sk-toCwQl8ObRPPSSB798751742EaF844658e83Aa9207A47c65'
]
BASE_URL = "https://free.v36.cm/v1/"

# 系统提示词  
SYSTEM_PROMPT = """You are a Midjourney prompt optimizer, tasked with making creatively rich modifications to prompts while ensuring quality is maintained.

Rules:

Replace the main subject with a creative alternative (keeping the same style/mood), and at the same time, creatively adjust more descriptive elements such as actions, scene layouts, decorative details, etc., to increase the range of modifications.
Keep all original parameters (--ar, --v, etc.) exactly unchanged.
Retain core detailed descriptions (lighting, textures, colors, materials, style, etc.), allowing for expansive modifications to non-core details.
Ensure "on white background" is included (add if missing).
Output only a single line, with no explanations.

Examples:
Input: "a cute cat sitting, soft lighting --ar 1:1"
Output: "a chubby rabbit hugging a carrot, curled up on a cushion, soft lighting on white background --ar 1:1"

Input: "ornate castle with rich architectural details, fantasy style --v 5 --ar 2:3"
Output: "ornate floating palace adorned with glowing vines, architectural details as intricate as a maze, fantasy style on white background --v 5 --ar 2:3"

Transform the following prompt:"""

# 全局变量用于统计
processed_count = 0
success_count = 0
error_count = 0
total_input_tokens = 0
total_output_tokens = 0
total_cost = 0.0
start_time = None
total_prompts = 0

# 双key速率限制配置
MAX_REQUESTS_PER_MINUTE_PER_KEY = 96
key_request_times = {0: [], 1: []}  # 为每个key单独记录请求时间
key_usage_count = {0: 0, 1: 0}  # 记录每个key的使用次数

# 控制台输出优化变量
last_rate_limit_message = {}  # 记录上次速率限制消息
rate_limit_count = {0: 0, 1: 0}  # 记录连续速率限制次数
last_progress_time = 0  # 上次显示进度的时间
current_rate_limit_status = {}  # 当前速率限制状态

# 价格配置（每1000个token的价格）
INPUT_PRICE_PER_1K = 0.00015  # $0.00015 / 1k tokens
OUTPUT_PRICE_PER_1K = 0.0006  # $0.0006 / 1k tokens

# 初始化tokenizer
try:
    tokenizer = tiktoken.encoding_for_model("gpt-4o-mini")
except:
    tokenizer = tiktoken.get_encoding("cl100k_base")

def count_tokens(text):
    """计算文本的token数量"""
    return len(tokenizer.encode(text))

def calculate_cost(input_tokens, output_tokens):
    """计算API调用成本"""
    input_cost = (input_tokens / 1000) * INPUT_PRICE_PER_1K
    output_cost = (output_tokens / 1000) * OUTPUT_PRICE_PER_1K
    return input_cost + output_cost

def format_time(seconds):
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds // 60
        secs = seconds % 60
        return f"{int(minutes)}分{int(secs)}秒"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        secs = seconds % 60
        return f"{int(hours)}小时{int(minutes)}分{int(secs)}秒"

def calculate_eta(processed, total, elapsed_time):
    """计算预计完成时间"""
    if processed == 0:
        return "计算中..."
    
    avg_time_per_item = elapsed_time / processed
    remaining_items = total - processed
    remaining_time = avg_time_per_item * remaining_items
    
    eta = datetime.now() + timedelta(seconds=remaining_time)
    return eta.strftime("%H:%M:%S")

def display_progress_stats(force=False):
    """显示进度统计信息"""
    global start_time, processed_count, success_count, error_count, total_cost, total_prompts, last_progress_time

    if start_time is None:
        return

    current_time = time.time()
    # 只有强制显示或者距离上次显示超过5秒才显示
    if not force and current_time - last_progress_time < 5:
        return

    last_progress_time = current_time
    elapsed_time = current_time - start_time
    progress_percent = (processed_count / total_prompts) * 100 if total_prompts > 0 else 0
    success_rate = (success_count / processed_count) * 100 if processed_count > 0 else 0

    # 计算处理速度（每分钟）
    speed_per_minute = (processed_count / elapsed_time) * 60 if elapsed_time > 0 else 0

    # 预计完成时间
    eta = calculate_eta(processed_count, total_prompts, elapsed_time)

    # 剩余时间估算
    if processed_count > 0 and processed_count < total_prompts:
        remaining_time = ((total_prompts - processed_count) / processed_count) * elapsed_time
        remaining_str = format_time(remaining_time)
    else:
        remaining_str = "即将完成"

    # 检查当前速率限制状态
    rate_limit_info = ""
    active_limits = []
    for key_idx in range(len(API_KEYS)):
        if key_idx in current_rate_limit_status:
            remaining_wait = current_rate_limit_status[key_idx] - current_time
            if remaining_wait > 0:
                active_limits.append(f"Key{key_idx+1}等待{remaining_wait:.1f}s")

    if active_limits:
        rate_limit_info = f" | 速率限制: {', '.join(active_limits)}"

    # 创建进度条
    bar_length = 30
    filled_length = int(bar_length * progress_percent / 100)
    bar = '█' * filled_length + '░' * (bar_length - filled_length)

    print(f"\r[{bar}] {progress_percent:.1f}% | "
          f"已处理: {processed_count}/{total_prompts} | "
          f"成功率: {success_rate:.1f}% | "
          f"速度: {speed_per_minute:.1f}/分钟 | "
          f"成本: ${total_cost:.4f} | "
          f"剩余时间: {remaining_str}{rate_limit_info}", end="", flush=True)

def print_rate_limit_status(key_idx, wait_time):
    """优化的速率限制状态显示"""
    global current_rate_limit_status

    current_time = time.time()
    current_rate_limit_status[key_idx] = current_time + wait_time

    # 只在第一次或等待时间显著变化时显示
    key_name = f"Key{key_idx+1}"
    if key_idx not in last_rate_limit_message or abs(last_rate_limit_message[key_idx] - wait_time) > 5:
        print(f"\n[等待] {key_name} 达到速率限制，等待 {wait_time:.1f}秒...")
        last_rate_limit_message[key_idx] = wait_time
        rate_limit_count[key_idx] = 1
    else:
        rate_limit_count[key_idx] += 1
        if rate_limit_count[key_idx] % 10 == 0:  # 每10次显示一次汇总
            print(f"\n[等待] {key_name} 持续等待中... (已等待{rate_limit_count[key_idx]}次)")

def select_input_file():
    """选择输入文件"""
    # 创建隐藏的根窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    root.attributes('-topmost', True)  # 设置置顶
    
    # 打开文件选择对话框
    input_file = filedialog.askopenfilename(
        title="选择提示词文件",
        filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
        parent=root
    )
    
    root.destroy()  # 销毁根窗口
    return input_file

def get_available_key():
    """获取可用的API key索引，优先选择使用次数较少的key"""
    current_time = time.time()
    
    # 清理超过1分钟的请求记录
    for key_idx in range(len(API_KEYS)):
        key_request_times[key_idx] = [t for t in key_request_times[key_idx] if current_time - t < 60]
    
    # 找到可用的key（未达到速率限制的key）
    available_keys = []
    for key_idx in range(len(API_KEYS)):
        if len(key_request_times[key_idx]) < MAX_REQUESTS_PER_MINUTE_PER_KEY:
            available_keys.append(key_idx)
    
    if not available_keys:
        # 如果所有key都达到限制，返回最早可用的key
        earliest_available_time = float('inf')
        best_key = 0
        for key_idx in range(len(API_KEYS)):
            if key_request_times[key_idx]:
                earliest_time = key_request_times[key_idx][0] + 60
                if earliest_time < earliest_available_time:
                    earliest_available_time = earliest_time
                    best_key = key_idx
        return best_key, earliest_available_time - current_time
    
    # 在可用的key中选择使用次数最少的
    best_key = min(available_keys, key=lambda k: key_usage_count[k])
    return best_key, 0

async def rate_limit_for_key(key_idx):
    """为特定key进行速率限制控制"""
    current_time = time.time()
    
    # 清理超过1分钟的请求记录
    key_request_times[key_idx] = [t for t in key_request_times[key_idx] if current_time - t < 60]
    
    # 如果请求数量达到限制，等待
    if len(key_request_times[key_idx]) >= MAX_REQUESTS_PER_MINUTE_PER_KEY:
        sleep_time = 60 - (current_time - key_request_times[key_idx][0])
        if sleep_time > 0:
            print_rate_limit_status(key_idx, sleep_time)
            await asyncio.sleep(sleep_time)
            # 清理速率限制状态
            if key_idx in current_rate_limit_status:
                del current_rate_limit_status[key_idx]
            # 重新清理请求记录
            current_time = time.time()
            key_request_times[key_idx] = [t for t in key_request_times[key_idx] if current_time - t < 60]
    
    # 记录当前请求时间
    key_request_times[key_idx].append(current_time)
    key_usage_count[key_idx] += 1

async def process_prompt_with_key(session, prompt, key_idx):
    """使用指定的API Key处理单个提示词"""
    global total_input_tokens, total_output_tokens, total_cost
    
    # 对选定的key进行速率限制
    await rate_limit_for_key(key_idx)
    
    # 使用选定的API key
    headers = {
        "Authorization": f"Bearer {API_KEYS[key_idx]}",
        "Content-Type": "application/json",
    }
    
    # 计算输入token数量
    system_tokens = count_tokens(SYSTEM_PROMPT)
    user_tokens = count_tokens(prompt.strip())
    input_tokens = system_tokens + user_tokens
    
    async with session.post(
        url=f"{BASE_URL}chat/completions",
        json={
            "model": "gpt-4o-mini",
            "max_tokens": 4000,
            "temperature": 0.5,
            "messages": [
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": prompt.strip()}
            ],
        },
        headers=headers,
        timeout=aiohttp.ClientTimeout(total=30)  # 30秒超时
    ) as response:
        if response.status == 200:
            result = await response.json()
            ai_response = result['choices'][0]['message']['content']
            
            # 计算输出token数量
            output_tokens = count_tokens(ai_response)
            
            # 计算本次请求成本
            request_cost = calculate_cost(input_tokens, output_tokens)
            
            # 更新全局统计
            total_input_tokens += input_tokens
            total_output_tokens += output_tokens
            total_cost += request_cost
            
            return True, ai_response, f"成功使用Key{key_idx+1}"
        else:
            error_text = await response.text()
            return False, None, f"HTTP错误 {response.status}: {error_text}, 使用Key{key_idx+1}"

async def process_prompt(session, semaphore, prompt, output_file):
    """处理单个提示词，带重试机制"""
    global processed_count, success_count, error_count
    
    async with semaphore:
        max_retries = 2  # 最多重试2次（总共3次尝试）
        last_error = None
        used_keys = []  # 记录已使用的key
        
        for attempt in range(max_retries + 1):
            try:
                # 获取可用的API key，优先选择未使用过的key
                available_keys = [i for i in range(len(API_KEYS)) if i not in used_keys]
                
                if not available_keys:
                    # 如果所有key都用过了，重置并选择使用次数最少的
                    used_keys = []
                    available_keys = list(range(len(API_KEYS)))
                
                # 从可用key中选择使用次数最少的
                key_idx = min(available_keys, key=lambda k: key_usage_count[k])
                used_keys.append(key_idx)
                
                # 检查是否需要等待速率限制
                _, wait_time = get_available_key()
                if wait_time > 0 and attempt > 0:
                    print(f"\n[重试] 等待速率限制 {wait_time:.1f}秒 (Key{key_idx+1})")
                    await asyncio.sleep(wait_time)

                # 尝试处理提示词
                success, result, message = await process_prompt_with_key(session, prompt, key_idx)

                if success:
                    # 成功处理
                    async with aiofiles.open(output_file, 'a', encoding='utf-8') as f:
                        await f.write(f"{result}\n")

                    success_count += 1
                    if attempt > 0:
                        print(f"\n[成功] 重试成功 (第{attempt+1}次尝试, Key{key_idx+1})")
                    break
                else:
                    # 处理失败
                    last_error = message
                    if attempt < max_retries:
                        # 还有重试机会，等待后重试
                        wait_time = min(2 ** attempt, 5)  # 指数退避，最多等待5秒
                        print(f"\n[失败] 第{attempt+1}次尝试失败，{wait_time}秒后重试...")
                        await asyncio.sleep(wait_time)
                    else:
                        # 最后一次尝试也失败了
                        print(f"\n[错误] 重试失败: {message}")
                        async with aiofiles.open(output_file, 'a', encoding='utf-8') as f:
                            await f.write(f"错误(重试{max_retries}次后失败): {last_error}\n")
                        error_count += 1
                        
            except asyncio.TimeoutError:
                last_error = f"请求超时，使用Key{key_idx+1}"
                if attempt < max_retries:
                    wait_time = min(2 ** attempt, 5)
                    print(f"\n[超时] 第{attempt+1}次尝试超时，{wait_time}秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    print(f"\n[超时] 重试超时: {last_error}")
                    async with aiofiles.open(output_file, 'a', encoding='utf-8') as f:
                        await f.write(f"错误(重试{max_retries}次后超时): {last_error}\n")
                    error_count += 1

            except Exception as e:
                last_error = f"请求异常: {e}"
                if attempt < max_retries:
                    wait_time = min(2 ** attempt, 5)
                    print(f"\n[异常] 第{attempt+1}次尝试异常，{wait_time}秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    print(f"\n[异常] 重试异常: {last_error}")
                    async with aiofiles.open(output_file, 'a', encoding='utf-8') as f:
                        await f.write(f"错误(重试{max_retries}次后异常): {last_error}\n")
                    error_count += 1
        
        processed_count += 1
        
        # 每处理100个或每10秒显示一次进度统计
        if processed_count % 100 == 0:
            display_progress_stats(force=True)
        else:
            display_progress_stats()

async def main():
    """主程序"""
    global processed_count, success_count, error_count, total_input_tokens, total_output_tokens, total_cost
    global start_time, total_prompts
    
    # 选择输入文件
    input_file = select_input_file()
    if not input_file:
        print("[错误] 未选择文件，程序退出！")
        return

    if not os.path.exists(input_file):
        print(f"[错误] 选择的文件 {input_file} 不存在！")
        return
    
    # 创建输出文件名（带时间戳）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"AI优化结果_双key_{timestamp}.txt"
    
    # 读取所有提示词
    async with aiofiles.open(input_file, 'r', encoding='utf-8') as f:
        content = await f.read()
        prompts = [line.strip() for line in content.split('\n') if line.strip()]
    
    total_prompts = len(prompts)
    start_time = time.time()
    start_datetime = datetime.now()
    
    print("=" * 80)
    print("批量提示词处理程序启动 (双Key版 + 智能重试)")
    print("=" * 80)
    print(f"输入文件: {input_file}")
    print(f"提示词数量: {total_prompts} 个")
    print(f"输出文件: {output_file}")
    print(f"开始时间: {start_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 80)
    print("配置信息:")
    print(f"  双API Key负载均衡")
    print(f"  每个Key速率限制: 每分钟最多 {MAX_REQUESTS_PER_MINUTE_PER_KEY} 次请求")
    print(f"  理论最大速度: 每分钟 {MAX_REQUESTS_PER_MINUTE_PER_KEY * 2} 次请求")
    print(f"  最大并发线程: 20 个")
    print("智能重试机制:")
    print("  最多重试 2 次 (总共3次尝试)")
    print("  自动切换API Key重试")
    print("  指数退避策略 (1s → 2s → 4s)")
    print("  请求超时: 30秒")
    print("  优先使用未用过的Key")
    print("=" * 80)
    
    # 创建输出文件
    async with aiofiles.open(output_file, 'w', encoding='utf-8') as f:
        await f.write("")  # 创建空文件
    
    # 创建信号量限制并发数为20（双key可以支持更高并发）
    semaphore = asyncio.Semaphore(20)
    
    # 创建HTTP会话
    async with aiohttp.ClientSession() as session:
        # 创建所有任务
        tasks = [
            process_prompt(session, semaphore, prompt, output_file) 
            for prompt in prompts
        ]
        
        # 执行所有任务
        await asyncio.gather(*tasks)
    
    # 计算总运行时长
    end_time = time.time()
    total_runtime = end_time - start_time
    end_datetime = datetime.now()
    
    print("\n" + "=" * 80)
    print("批量处理完成！")
    print("=" * 80)
    print("最终统计:")
    print(f"  开始时间: {start_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"  结束时间: {end_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"  总运行时长: {format_time(total_runtime)}")
    print(f"  总数量: {total_prompts}")
    print(f"  成功: {success_count}")
    print(f"  失败: {error_count}")
    print(f"  成功率: {(success_count/total_prompts)*100:.2f}%")
    print(f"  平均速度: {(total_prompts/total_runtime)*60:.1f}个/分钟")
    print("-" * 80)
    print("重试机制统计:")
    print("  最多重试次数: 2次")
    print("  重试策略: 自动切换API Key + 指数退避")
    print("  超时设置: 30秒")
    print("-" * 80)
    print("Key使用统计:")
    print(f"  Key1: {key_usage_count[0]}次")
    print(f"  Key2: {key_usage_count[1]}次")
    print(f"  负载均衡比例: Key1={key_usage_count[0]/sum(key_usage_count.values())*100:.1f}%, Key2={key_usage_count[1]/sum(key_usage_count.values())*100:.1f}%")
    print("-" * 80)
    print("成本统计:")
    print(f"  总输入tokens: {total_input_tokens:,}")
    print(f"  总输出tokens: {total_output_tokens:,}")
    print(f"  总成本: ${total_cost:.6f}")
    print("=" * 80)
    print(f"结果已保存到: {output_file}")
    print("=" * 80)

if __name__ == "__main__":
    asyncio.run(main())