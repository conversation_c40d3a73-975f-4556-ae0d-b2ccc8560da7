import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入质量检测函数
exec(open('批量提示词处理程序_双key版.py', encoding='utf-8').read().split('def is_valid_result')[0])

def is_valid_result(result):
    """检测生成结果是否有效"""
    if not result or not isinstance(result, str):
        return False
    
    # 去除空白字符后检查长度
    cleaned_result = result.strip()
    if len(cleaned_result) < 50:
        return False
    
    # 检查是否以不完整的内容开头（如单个字母、标点符号等）
    if cleaned_result.startswith(('s ', 'a ', 'an ', 'the ', ',', '.', '!', '?', ';', ':')):
        return False
    
    # 检查是否包含基本的提示词元素
    if not any(keyword in cleaned_result.lower() for keyword in ['--ar', '--v', 'background', 'style', 'lighting']):
        return False
    
    # 检查是否包含实际的描述性内容（不只是参数）
    # 移除所有参数后检查剩余内容
    content_without_params = cleaned_result
    for param in ['--ar', '--v', '--s', '--c', '--q', '--style', '--chaos', '--quality']:
        import re
        content_without_params = re.sub(rf'{param}\s+[\w\d:./]+', '', content_without_params, flags=re.IGNORECASE)
    
    # 移除 "on white background" 等固定短语后检查
    content_without_params = re.sub(r'\s*on white background\s*', ' ', content_without_params, flags=re.IGNORECASE)
    content_without_params = content_without_params.strip()
    
    # 检查剩余内容是否足够（至少20个字符的描述性内容）
    if len(content_without_params) < 20:
        return False
    
    # 检查是否包含常见的描述性词汇
    descriptive_words = ['beautiful', 'elegant', 'stunning', 'detailed', 'intricate', 'ornate', 'cute', 'majestic', 
                        'vibrant', 'serene', 'dramatic', 'mystical', 'ancient', 'modern', 'vintage', 'futuristic',
                        'woman', 'man', 'cat', 'dog', 'castle', 'house', 'landscape', 'portrait', 'scene']
    
    if not any(word in cleaned_result.lower() for word in descriptive_words):
        return False
    
    return True

# 测试用例
test_cases = [
    # 垃圾结果（应该被过滤）
    ("s on white background --ar 1:1", False, "不完整的开头"),
    ("a beautiful scene", False, "太短"),
    ("--ar 1:1 --v 5", False, "只有参数没有描述"),
    ("on white background --ar 1:1", False, "没有实际描述内容"),
    ("the lighting is good --ar 1:1", False, "描述性内容不足"),
    
    # 有效结果（应该通过）
    ("a beautiful elegant woman in vintage dress, soft lighting on white background --ar 1:1", True, "完整有效的提示词"),
    ("cute cat sitting on a cushion, detailed fur texture, warm lighting on white background --ar 1:1", True, "包含描述性词汇"),
    ("ornate castle with intricate architectural details, fantasy style on white background --v 5 --ar 2:3", True, "复杂描述"),
    ("stunning landscape with majestic mountains and serene rivers, dramatic lighting --ar 16:9", True, "风景描述"),
]

print("质量检测测试结果:")
print("=" * 60)

for i, (text, expected, description) in enumerate(test_cases, 1):
    result = is_valid_result(text)
    status = "✓" if result == expected else "✗"
    print(f"{status} 测试 {i}: {description}")
    print(f"   输入: {text}")
    print(f"   期望: {'有效' if expected else '无效'} | 实际: {'有效' if result else '无效'}")
    if result != expected:
        print(f"   ❌ 测试失败!")
    print()
